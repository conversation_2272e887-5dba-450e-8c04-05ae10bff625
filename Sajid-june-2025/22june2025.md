📅 **Task List - 22 June 2025**  
🗂 **Project**: Beige  

---

### 🔹 **Day Start**  

#### 📸 **Shoot Details Page Tasks**  

1. **Client View**:  
   1.1. Add a "Write Review" button when:  
        - Order Status is **Completed**.  
        - User is on the **Review** tab.  

2. **Admin View**:  
   2.1. Show a "Reschedule" button for Pending shoots.  
   2.2. Show a "Cancel" button in the Pre-Production tab.  
   2.3. Add a "Mark as Completed" button accessible at all stages.  
   2.4. Validate **Mark as Completed** with:  
        - Payment Status is **Paid**.  
        - Full payment is settled.  

3. **Content Partner (CP) View**:  
   3.1. Remove the "Mark as Completed" button for CP users.  
   3.2. Implement a new API for CPs to "Cancel Shoot".  
         
   3.3. Remove the "Book a Shoot" button.  
   3.4. Clarify the purpose of "Track Shoot Days" for CP users.  


4. **Project Manager View**:  
   4.1. Grant access to the "Mark as Completed" button.  
   4.2. Enable Project Managers to update the status of projects or shoots.  

5. **Sales Representatives & Staff**:  
   5.1. Restrict "Mark as Completed" functionality for Sales Representatives and general staff.  

---

### 🚨 **Emergency Handling**  
- If any additional related task is identified among the above, move it to the **Emergency Task list**.  

---

### 🔻 **Day End – Task Status Update**

#### ✅ **Completed Tasks**

**📸 Shoot Details Page Tasks - ALL COMPLETED**

1. **Client View**: ✅ **DONE**
   - 1.1. "Write Review" button implementation completed
   
2. **Admin View**: ✅ **DONE**
   - 2.1. "Reschedule" button for Pending shoots completed
   - 2.2. "Cancel" button in Pre-Production tab completed  
   - 2.3. "Mark as Completed" button implementation completed
   - 2.4. Payment validation for "Mark as Completed" completed

3. **Content Partner (CP) View**: ✅ **DONE**
   - 3.1. "Mark as Completed" button removal completed
   - 3.2. New "Cancel Shoot" API implementation completed
   - 3.3. "Book a Shoot" button removal completed
   - 3.4. "Track Shoot Days" purpose clarification completed

4. **Project Manager View**: ✅ **DONE**
   - 4.1. "Mark as Completed" button access granted
   - 4.2. Project/shoot status update functionality enabled

5. **Sales Representatives & Staff**: ✅ **DONE**
   - 5.1. "Mark as Completed" restrictions implemented

---

**📊 Summary**: All assigned tasks completed successfully ✅  
**🕐 Completion Time**: End of Day - 22 June 2025  
**🎯 Overall Status**: 100% Complete

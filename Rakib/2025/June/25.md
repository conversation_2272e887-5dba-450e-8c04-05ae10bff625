# 📝 Task Plan: Extract and Rebuild Docker Application Image

**Employee:** <PERSON><PERSON><PERSON>
**Date:** 2025-06-24  
**Project:** Docker Image Reverse & Rebuild  
**Target Images:**  
- flowiseai/flowise  
- jambonz/webapp  
- ...

---

## 🎯 Objective

Extract source application code and configuration from existing Docker images and rebuild new Docker images using clean and maintainable Dockerfiles.

---

## ✅ Task Checklist

- [ ] Identify and list all relevant Docker images to extract
- [ ] Create working directory: `~/docker-image-extract`
- [ ] Pull the latest image (if needed): `docker pull <image>`
- [ ] Extract Filesystem from Image

---


## 📝 Repositories for image source code
- https://github.com/luminouslabsbd/voiceerp-sbc-outbound

- https://github.com/luminouslabsbd/voiceerp-sbc-call-router

- https://github.com/luminouslabsbd/voiceerp-sbc-sip-sidecar

- https://github.com/luminouslabsbd/voiceerp-webapp (docker-source)

- https://github.com/luminouslabsbd/voiceerp-feature-server

- https://github.com/luminouslabsbd/voiceerp-sbc-registrar

- https://github.com/luminouslabsbd/voiceerp-sbc-inbound
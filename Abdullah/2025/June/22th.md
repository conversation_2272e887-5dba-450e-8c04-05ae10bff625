# Task Update
# 22 June

# Project: Rewardrace

# 🚧 Project Tasks – Shopify App Update

## 📬 1. Review Latest Shopify Email for Compliance (Done)
- [ ] Go through the latest email from Shopify to identify any new rules or required actions.
- [ ] Confirm alignment with:
  - [ ] [Expectations for participating on the Shopify App Store](https://shopify.dev/docs/apps/store/requirements)
  - [ ] [Managing app reviews effectively](https://shopify.dev/docs/apps/store/reviews)
  - [ ] [Eligibility for homepage/admin featuring](https://shopify.dev/docs/apps/store/marketing)

---

## ⚙️ 2. Functional Zero-Configuration Onboarding (Partially Done)
- [x] Add sample reward data (✅ Done)
- [ ] Add sample rule data (Redeem rull)
- [ ] Add default email settings on app install (Signup, purchase and redeem)
- [ ] Ensure seamless onboarding without any manual config
- [ ] Show a welcome message or quick-start guide on first access

---

## 📧 3. Email Settings Enhancements

### 🏷️ Add Dynamic Tag: Coupon Code
- [ ] Add new dynamic tag support: `{{coupon_code}}` in email templates
- [ ] Update documentation or UI helper showing available tags:
  - `{{first_name}}`
  - `{{last_name}}`
  - `{{shop_name}}`
  - `{{coupon_code}}` ✅ *(new)*

### 🔁 Replace Tag Logic in Backend
- [ ] Update email template rendering logic:
  - [ ] Detect and replace `{{coupon_code}}` with the actual code tied to customer/referral
  - [ ] Ensure fallbacks exist if the coupon is missing
- [ ] Sample implementation:
  ```js
  template = template
    .replace(/{{first_name}}/g, user.first_name)
    .replace(/{{last_name}}/g, user.last_name)
    .replace(/{{shop_name}}/g, shop.name)
    .replace(/{{coupon_code}}/g, coupon.code || '')

## 4. SaveBar Integration
 - [ ] Add SaveBar component from Shopify Polaris
 - [ ] Show SaveBar when form changes are detected in email settings
 - [ ] Implement onSave and onDiscard handlers
 - [ ] Ensure SaveBar is fully responsive and accessible
 - [ ] Test SaveBar flow across major browsers

# Task Update
# 29 June

# Project: Rewardrace

## Free Referral Feature with Rolling 30-Day Limits

## Objective

Launch a limited referral program for Free Plan merchants, allowing:
- 1 referral link per rolling 30-day window
- Up to 10 successful referee completions per window
- Auto-assignment to "All Customers" only
- Seamless integration with existing reward lifecycle and UI structure

## Summary of What’s Already Implemented

- Referral link listing (`_index.jsx`): Implemented
- VIP/specific customer targeting disabled: Implemented
- Tooltip shown for All Customers-only: Implemented
- Link creation with rewards (`generate_referral.jsx`): Implemented
- Referral reward lifecycle using enums: Implemented
- Free plan subscription block removed: Implemented
- Usage meter UI (e.g., 5 / 10): **Not implemented – must be added**

## Goals & Success Criteria

- 1 referral link per 30 days: Enforced
- Max 10 referee completions per link: Enforced
- Enforce "All Customers" only on Free Plan: Applied
- Display usage meter in UI: To be implemented
- UI restricts creation during active window: Enforced

## Free Plan Limitations

- Referral link creation: 1 every 30 days
- Max successful referrals: 10 per link
- Assign to VIP tiers: Disabled
- Assign to specific customers: Disabled
- Assign to all customers: Only option
- Usage counter reset: After 30 days
- Unused carryover: Not allowed
- Subscription restriction: Removed for referrals

## Referral Lifecycle

- Link visited -> `referee_reward_status = PENDING`
- Coupon redeemed by referee -> `referee_reward_status = COMPLETE` (counts as 1 usage)
- Referrer gets reward -> `referrer_reward_status = PENDING`
- Referrer redeems reward -> `referrer_reward_status = COMPLETE`

```js
export const referralRewardStatus = Object.freeze({
  PENDING: "1",
  COMPLETE: "2",
});



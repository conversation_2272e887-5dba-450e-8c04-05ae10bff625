# Task Update
# 24 June

# Project: Rewardrace

## 1. SaveBar Integration
 - [ ] Show SaveBar when form changes are detected in email settings (add new two field button color and text)
 - [ ] Test SaveBar flow across major browsers

## 2. 🧩 Image Upload Feature (Multi-Module)

## 🧠 Objective
Introduce image upload functionality for **paid merchants** across 3 key modules:

1. ✅ VIP Tier
2. ⏳ Customer Widget
3. ⏳ Reward Earn & Redeem Settings

---

## 🔐 Common Rule Across All Modules
- 📌 Feature restricted to **paid merchants only**
- ☁️ Images stored on cloud
- 🖼️ File types: .jpg, .png
- 📏 Size limit: Max 2MB (configurable)


updated_prd_text = """
📄 PRD: Upload VIP Tier Image to DigitalOcean Spaces (Updated)

File: app/routes/app.vip_program.create_vip_tier.jsx
Scope: Only image upload to DO Spaces from VIP Tier form
Status: All other features — including image drop/upload UI — are already working.

🎯 Goal
Enable image upload from the VIP Tier form to DigitalOcean Spaces. Once uploaded, save the image URL in formState.headerImage, and make sure the SaveBar is triggered.

✅ Functional Requirements

1. ✅ Image Upload Trigger (Already Implemented)
- DropZone handles file selection and validation:
  - Accepted types: image/jpeg, image/png, image/webp, image/gif
  - Max size: 5MB
- Triggers handleImageUpload() with selected file and folder name

2. Destination
- Cloud provider: DigitalOcean Spaces
- Bucket: redrace
- Region: fra1
- Upload folder format: 
  [shopDomain]_vip_tier/
  Example: abdullah-publish.myshopify.com_vip_tier/17192182734-banner.png

3. Upload API Endpoint
- A Remix action() endpoint at /api/uploadImage
- Accepts:
  - file (image file)
  - folder (subdirectory name)
- Returns:
  { "url": "https://redrace.fra1.digitaloceanspaces.com/[folder]/[filename]" }

4. Frontend Integration
- In handleImageUpload, calls:
  await uploadToCloud(file, folderName);
- After success:
  - Set the returned URL in formState.headerImage
  - Update files state for UI preview
  - Trigger SaveBar by marking form as dirty

5. Server-Side Upload Logic
- Uses AWS SDK v3 (@aws-sdk/client-s3)
- Reads config from environment:
  DO_SPACES_KEY=
  DO_SPACES_SECRET=
  DO_SPACES_BUCKET=redrace
  DO_SPACES_REGION=fra1
  DO_SPACES_ENDPOINT=https://fra1.digitaloceanspaces.com
- Upload settings:
  - ContentType based on file MIME
  - ACL: public-read

🧪 Validation
- ✅ Upload returns valid public image URL
- ✅ URL is saved in formState.headerImage
- ✅ SaveBar shows on change
- ✅ URL is saved with form submission
- ✅ Uploaded image is visible/previewed in UI

🧠 Optional Future Enhancements
- Use pre-signed URLs (for secure, client-side upload)
- Image optimization before upload
- Deletion and re-upload support
- Retry logic on failure
"""
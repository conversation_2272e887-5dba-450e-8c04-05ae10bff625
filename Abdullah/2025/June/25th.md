# Task Update
# 25 June

# Project: Rewardrace

## 1. 🧩 Image Upload Feature (Multi-Module)

## 🧠 Objective
Introduce image upload functionality for **paid merchants** across 3 key modules:

1. ✅ VIP Tier
2. ⏳ Customer Widget
3. ⏳ Reward Earn & Redeem Settings

---

## 🔐 Common Rule Across All Modules
- 📌 Feature restricted to **paid merchants only**
- ☁️ Images stored on cloud
- 🖼️ File types: .jpg, .png
- 📏 Size limit: Max 2MB (configurable)

## 2. 🧩 Onsite Content Visibility & Validation

## 🧠 Objective
Ensure all loyalty program-related onsite components are correctly implemented and **visible at the intended locations** (especially checkout, account page, and storefront).

---

## 🧪 Audit Scope & Issues Reported

| Feature                              | Status         | Target Location     |
|--------------------------------------|----------------|---------------------|
| ✅ Point balance before purchase     | ❌ Not showing | Checkout Page       |
| ✅ Points after purchase             | ❌ Not showing | Checkout or Thank You Page |
| ✅ Customer birthday set             | ✅ Working     | Account Page        |
| ✅ Customer retention (login prompt) | ⚠️ Incomplete  | Anywhere/storefront |

---

## 🔍 Tasks by Section

---

### (i) Checkout Page – Loyalty Points Display

#### Objective:
Display point balance *before* and *after* purchase in the **checkout** or **thank you page**.

#### Tasks:
- [ ] Audit where loyalty app scripts or Liquid snippets are injected
- [ ] Check if Shopify Plus is required to modify checkout
- [ ] Validate access to checkout.liquid (only for Shopify Plus)
- [ ] Fallback: Use `order status page` for "after purchase" points
- [ ] Ensure correct customer ID and point balance is fetched
- [ ] Show message like: You will earn 50 points from this purchase!


---

### (ii) Account Page – Birthday Field Validation

#### Objective:
Ensure customers can set/update their birthday in account page.

#### Tasks:
- [ ] Validate birthday field is present in customer account form
- [ ] Check if birthday is stored in metafield or customer tag
- [ ] Ensure value is correctly saved to DB
- [ ] Optional: Add birthday reward preview or points info

---

### (iii) General Site – Customer Retention Prompt

#### Objective:
Show an engaging message/banner for **guest users** to log in and access loyalty benefits.

#### Tasks:
- [ ] Add call-to-action for login/register on homepage or banner: Unlock exclusive rewards! Login to view your points balance and redeem offers.
- [ ] Conditional render based on `Shopify.customer` object
- [ ] Optional: Add dynamic teaser like “You’ve missed 200 points!”

---

### 4️⃣ Post-Purchase Page – Points Earned

#### Objective:
Display earned points after successful purchase

#### Tasks:
- [ ] Inject loyalty message in **order status page** (if checkout.liquid not available)
- [ ] Use Shopify’s ScriptTag API or post-checkout webhook
- [ ] Show:

### (iv) Post-Purchase Page – Points Earned

#### Objective:
Display earned points after successful purchase

#### Tasks:
- [ ] Inject loyalty message in **order status page** (if checkout.liquid not available)
- [ ] Use Shopify’s ScriptTag API or post-checkout webhook
- [ ] Show: Thank you! You've earned 100 points from this order.






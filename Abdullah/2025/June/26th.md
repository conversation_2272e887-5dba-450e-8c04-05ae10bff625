# Task Update
# 26 June

# Project: Rewardrace

## 1. 🧩 Onsite Content Visibility & Validation

## 🧠 Objective
Ensure all loyalty program-related onsite components are correctly implemented and **visible at the intended locations** (especially checkout, account page, and storefront).

---

## 🧪 Audit Scope & Issues Reported

| Feature                              | Status         | Target Location     |
|--------------------------------------|----------------|---------------------|
| ✅ Point balance before purchase     | ❌ Not showing | Checkout Page       |
| ✅ Points after purchase             | ❌ Not showing | Checkout or Thank You Page |
| ✅ Customer birthday set             | ✅ Working     | Account Page        |
| ✅ Customer retention (login prompt) | ⚠️ Incomplete  | Anywhere/storefront |

---

## 🔍 Tasks by Section

---

### (i) Checkout Page – Loyalty Points Display

#### Objective:
Display point balance *before* and *after* purchase in the **checkout** or **thank you page**.

#### Tasks:
- [ ] Audit where loyalty app scripts or Liquid snippets are injected
- [ ] Check if Shopify Plus is required to modify checkout
- [ ] Validate access to checkout.liquid (only for Shopify Plus)
- [ ] Fallback: Use `order status page` for "after purchase" points
- [ ] Ensure correct customer ID and point balance is fetched
- [ ] Show message like: You will earn 50 points from this purchase!

### (ii) Post-Purchase Page – Points Earned

#### Objective:
Display earned points after successful purchase

#### Tasks:
- [ ] Inject loyalty message in **order status page** (if checkout.liquid not available)
- [ ] Use Shopify’s ScriptTag API or post-checkout webhook
- [ ] Show: Thank you! You've earned 100 points from this order.

## 2. 🧩 VIP Tier Reward Display & Icon Selection Fix

## 🧠 Objective
Ensure that each VIP Tier:
- 🎯 Shows only its associated rewards correctly
- 🖼️ Displays selected icons properly (and allows re-selection)

## 🔍 Problem Summary

| Feature                      | Current Issue                                |
|------------------------------|----------------------------------------------|
| Tier-based reward filtering  | ❌ Rewards show incorrectly or not filtered   |
| Icon selection               | ⚠️ Icons selectable but not showing/linked   |

### (i) Rewards Not Showing Per Tier Level

#### Objective:
Ensure each VIP tier displays its **correctly assigned rewards** only.

#### Tasks:
- [ ] Check DB structure: verify if rewards have `tier_id` or tier linkage field
- [ ] Audit reward-tiers relationship (1-to-many or many-to-many)
- [ ] validate if rewards are returned in backend API/controller
- [ ] Map rewards to correct tier in UI (match tier ID in component)
- [ ] Fallback: show "No rewards assigned to this tier" if empty


(ii) Icon Selection (QA & Display)
Objective:
Fix icon rendering logic per tier and ensure correct upload/selection behavior

Tasks:
- [ ] Review how icon is stored (in tier.icon or related object)
- [ ] Validate frontend correctly displays:
- [ ] Pre-selected icon (on edit)
- [ ] Newly uploaded icon
- [ ] Allow re-upload or removal of tier icon

## 3. Add image upload and delete logic to tier detail page
## 4. Add survey document to make task plan

# Task Update
# 23 June

# Project: Rewardrace

## 📧 1. Email Settings Enhancements

### 🏷️ Add Dynamic Tag: Coupon Code
- [ ] Add new dynamic tag support: `{{coupon_code}}` in email templates
- [ ] Update documentation or UI helper showing available tags:
  - `{{first_name}}`
  - `{{last_name}}`
  - `{{shop_name}}`
  - `{{coupon_code}}` ✅ *(new)*

### 🔁 Replace Tag Logic in Backend
- [ ] Update email template rendering logic:
  - [ ] Detect and replace `{{coupon_code}}` with the actual code tied to customer/referral
  - [ ] Ensure fallbacks exist if the coupon is missing
- [ ] Sample implementation:
  ```js
  template = template
    .replace(/{{first_name}}/g, user.first_name)
    .replace(/{{last_name}}/g, user.last_name)
    .replace(/{{shop_name}}/g, shop.name)
    .replace(/{{coupon_code}}/g, coupon.code || '')

## 2. SaveBar Integration
 - [ ] Add SaveBar component from Shopify Polaris
 - [ ] Show SaveBar when form changes are detected in email settings
 - [ ] Implement onSave and onDiscard handlers
 - [ ] Ensure SaveBar is fully responsive and accessible
 - [ ] Test SaveBar flow across major browsers

## 3 📋 Purchase Reward Summary UI – Type-Based Logic

## 📌 Objective
Update the Purchase Reward summary display logic to dynamically reflect reward type:
- If reward type is **"each"**, show "**X points per $1 spent**"
- If reward type is **"fixed"**, show "**X points per order**"

### 🎯 Purchase Reward Type Variants
- **Type: "each"** → Reward points per dollar spent
- **Type: "fixed"** → Flat reward points per purchase

### 🧠 Current Behavior
- Purchase reward configuration is saved correctly with `purchase_reward_type` and `purchase_points`
- Summary table always shows static point display (needs to be dynamic)

### 🔍 Analyze Existing Table
- [ ] Identify the component/page where reward summary is rendered
- [ ] Locate source of `purchase_reward_type` and `purchase_points`

### 🔁 Update Conditional Display Logic
- [ ] If `purchase_reward_type === 'each'`:
  - Display: **"X points per $1 spent"**
- [ ] Else (fixed):
  - Display: **"X points per order"**

```js
const displayText =
  purchase_reward_type === 'each'
    ? `${purchase_points} points per $1 spent`
    : `${purchase_points} points per order`;

## 4. Referral Program: Multi-Segment Assignment Support

### 🎛️ Admin UI Updates
- [ ] Refactor referral link assignment UI
  - [ ] Multi-select modal for customer assignment
  - [ ] VIP tag filter or checkbox
  - [ ] Clear sectioning: 
    - Assign to specific customers
    - Assign to multiple customers
    - Assign via VIP tag
  - [ ] Add frontend validation to prevent duplicate assignments

### 🧠 Backend Logic
- [ ] Update schema to support multiple assignment types (e.g., via a `referral_link_assignments` table)
- [ ] Accept and save array-based customer assignments
- [ ] Handle VIP-based assignment using customer tags
- [ ] Validate all customer IDs before storing

### 🔍 Referral Claim & Tracking Logic
- [ ] On referral link usage:
  - [ ] Match user against directly assigned customers
  - [ ] Match user against VIP-tagged assignment
- [ ] Prevent impersonation or misuse of referral links
  - [ ] Ensure the user is actually tagged as VIP if using a VIP-based referral
  - [ ] Confirm identity via customer ID or email hash

---

## 📝 Notes & Next Steps
- Ensure deduplication of customers across assignment types (no double rewards).
- Add fallback logic in case of missing tags or deleted customers.
- Consider optional v2 enhancements:
  - Referral performance analytics
  - Referral expiration dates
  - Reward customization by segment
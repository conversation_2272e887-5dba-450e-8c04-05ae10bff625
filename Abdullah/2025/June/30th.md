# Task Update
# 30 June

# Project: Rewardrace

## 1. 🧩 Free Referral Feature with Rolling 30-Day Limits

🧠 Objective
Offer a limited referral system for free plan merchants with strict rules:
- ✅ One referral link per rolling 30-day window
- ✅ Up to 10 successful referrals within that window
- 🚫 Cannot assign to specific customers or VIP tiers
- ✅ Only assignable to "All Customers"

🔒 Free Plan Limitations Overview

| Feature                          | Free Plan Behavior         |
|----------------------------------|-----------------------------|
| Referral link creation           | ✅ 1 per 30 days            |
| Max referral uses                | ✅ 10                      |
| Assign to VIP tiers              | ❌ Disabled                |
| Assign to specific customers     | ❌ Disabled                |
| Assign to all customers          | ✅ Only option             |
| Usage counter reset              | ✅ After 30 days           |
| Unused referrals carry over      | ❌ Not allowed             |

🧩 Core Functional Tasks

(i) Plan Access Control

Tasks:
- Determine merchant's billing plan
- If plan === free:
  - Enforce restrictions:
    - Only 1 referral link per 30-day window
    - Max 10 referral uses per link
    - Only allow "All Customers" assignment
    - Disable VIP tier and customer-specific options

(ii) Referral Link Creation (Rolling Window)

Tasks:
- On referral link creation:
  - Store created_at timestamp
  - Calculate expires_at = created_at + 30 days
  - Set usage_count = 0
- Before allowing new link:
  - Check if currentDate >= expires_at
    - ✅ If yes → allow link creation
    - ❌ If not → block and show: "You can create your next referral link on July 15, 2025"
- Prevent multiple links per window

(iii) Referral Usage Limiting (Max 10)

Tasks:
- Track usage_count per link
- On each successful referral:
  - Increment usage_count
  - If usage_count >= 10:
    - ❌ Block further usage
    - Show message: “Referral limit reached for this cycle”
- Reset usage only after new window starts

(iv) UI/UX Adjustments

Tasks:
- Display usage meter: 5 / 10 referrals used
- Display next eligible creation date: July 15
- Disable creation form if link exists in current window
- Grey out VIP Tier selection and Specific customer targeting
- Show tooltip/info: "Free plan includes 1 referral link and 10 referrals per 30 days."


(v) 🔮 Optional Future Features
- Notify merchant by email when referral limit is hit
- Show referral conversion analytics in dashboard
- Allow partial usage insights per referrer

## 2.Survey Feature Update with Rolling 30-Day Free Plan Limits

Feature Overview

Area                    | Description
------------------------|---------------------------------------------
Survey Creation         | Merchants can create a feedback survey.
Slide Management        | Each survey can include multiple slides (questions).
Placement               | Survey shown on the Product Detail Page.
Status                  | Surveys are inactive by default.
Free Plan Limits        | 1 survey per rolling 30 days, max 10 responses per cycle.

Functional Tasks

# (i) UI Refactor

Page                  | Description                                | Reference Page               | Time
----------------------|--------------------------------------------|------------------------------|------
Survey Creation Page  | Redesign like Referral Creation Page       | referral.create.jsx          | 2h
Slide Editor Page     | Redesign like Widget Customization Page    | widget.customize.jsx         | 2h

# (ii) Survey Creation Logic with Rolling Limits

Logic:
- Merchant can only create 1 survey every 30 days.
- Store created_at and calculate expires_at = created_at + 30 days.
- Block new creation if now < expires_at.

Tasks:
- On survey creation: set timestamps, set response_count = 0
- On new attempt: block with message if window not passed

Time: 3.5h

# (iii) Response Count Limiting (Max 10)

Behavior:
- Track and limit customer responses per survey.
- Reset counter only when new survey is created.

Tasks:
- Track response_count
- Increment on submission, block at 10
- Show quota message

Time: 4.5h

# (iv) Activation Flow

Behavior:
- Survey created as inactive
- Merchant must activate to show to customers

Tasks:
- Set status to inactive by default
- Add toggle in UI
- Filter visibility

Time: 1.5h

Optional Enhancements

- Show usage progress bar (e.g. 6/10)
- Show countdown to next creation
- Email alerts when quota hit
- Survey analytics dashboard
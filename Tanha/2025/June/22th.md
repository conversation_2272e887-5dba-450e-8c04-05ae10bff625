## 📅 Task List - 22 June 2025
### 🗂 Project: **Beige**

---

### 🔹 Day Start

### ✅ API Development Tasks (Planned)

1. [ ] Implement **Order Status API** based on defined business logic.
2. [ ] Create **Shoot Cancel API** specifically for **CPs (Content Providers)**.
3. [ ] Add **Role-Based User Module API** for access control.
4. [ ] Update logic for **CP Profile Shoots List API**.
5. [ ] Cross-check the existing **Due Module API** — confirm if any part is missing.
6. [ ] Develop **Email Verification Module API** for both **CPs** and **Users**.
7. [ ] Implement **Review Retrieval API** based on `order_id`.

---

### 🚨 Emergency Handling
- [ ] If any additional related task is identified among the above, move it to the **Emergency Task** list.

---

### 🔻 Day End – Task Status Update


### ✅ Completed Tasks

- [ ] Implemented **Order Status API** based on defined business logic.
- [ ] Created **Shoot Cancel API** specifically for **CPs (Content Providers)**.
- [ ] Added **Role-Based User Module API** for access control.
- [ ] Updated logic for **CP Profile Shoots List API**.
- [ ] Cross-checked the existing **Due Module API** and confirmed all parts are complete.
- [ ] Developed **Email Verification Module API** for both **CPs** and **Users**.
- [ ] Implemented **Review Retrieval API** based on `order_id`.
- [ ] Implemented the **Formget Password & Reset Password Module API**.
- [ ] Implemented the **CPs Registration OTP Verification Module API**.
- [ ] Developed **CP Order Cancel Logic API**.
- [ ] Integrated **Google Login** and **Facebook Login OAuth** backend code.



# 🚧 Project: Zerodel

## 📝 Task: Slider Image and Dynamic Text Management

## ✅ Task Summary

> [ClickUp Task Link](https://app.clickup.com/t/86ettt7vg)

- 🎯 Implement backend control for slider image and accompanying dynamic text.
- 📡 Provide API endpoint to deliver slider content to frontend.
- 🔜 Prepare to transition to the next assigned task.

---

### 📅 Day End Review – Zerodel

- ✅ Slider Image and Dynamic text manage from backend done https://app.clickup.com/t/86ettt7vg
- ✅ Provided api to show in frontend
- ✅ Fixed language code required error
- Withdraw fund option in customer profile wallet, beside the add fund option https://app.clickup.com/t/86etwramg (In progress)

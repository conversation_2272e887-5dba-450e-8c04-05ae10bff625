# 🚧 Project: ArchAngel

## 📝 Task: Handle Missing Columns in Sanction Models During Cron Execution

### 🎯 Objective
Ensure all sanction model fields are up-to-date with the incoming data structure during cron-based data ingestion.

---

### ✅ Task Steps

1. 🔄 **Run the cron-based update process**  
   Trigger the cron job that pulls and updates sanction model data.

2. 🛑 **Identify missing fields**  
   Watch for errors like:  
   `Invalid field name(s) for model: 'field_name'`

3. 🔍 **Compare incoming data vs. model definitions**  
   Match incoming JSON keys with existing Django model fields.

4. 🧱 **Update models with missing fields**  
   Add any missing fields using appropriate Django field types (e.g., `J<PERSON><PERSON>ield`, `Char<PERSON><PERSON>`, etc.).

5. 🛠️ **Make and apply migrations**  
   Run the following commands:
   ```bash
   python manage.py makemigrations
   python manage.py migrate


# 🚧 Project: Zerodel

## 📝 Task: Setup project in local environment, Understand the follow and start making APIs

---



### 📅 Day End Review – ArchAngel

- ✅ Added missing columns in sanction models to resolve issues during cron execution  
- ✅ Successfully applied migrations and verified data ingestion works without errors

---

### 📅 Day End Review – Zerodel

- ✅ Completed local environment setup  
- ✅ Explored and documented the file structure  
- ✅ Generated `project_overview.md` for internal documentation

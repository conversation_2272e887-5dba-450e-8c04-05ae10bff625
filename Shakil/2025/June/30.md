# Task Plan - June 30th, 2025


# Supplier SFTP Integration - Execution Plan

## Task Summary
**Goal:** Upgrade FTP to SFTP, remove app purchase requirement, enable bidirectional file transfer for suppliers.

**Context:** Product import feature at `/admin/drm_imports`

## Implementation Steps

### 1. Database Updates
- Add SFTP fields to credentials table
- Update FTP credential model

### 2. SFTP Services
- Create `SFTPService` class using existing SFTP library
- Update existing FTP services for SFTP support
- Maintain FTP backward compatibility

### 3. Controllers & UI
- Update FTP controller with SFTP options
- Add SFTP to import controller and source types
- Update FTP interface views for SFTP

### 4. Bidirectional SFTP Server
- Create supplier directory management
- Implement upload/download separation
- Add directory isolation between suppliers

### 5. Remove App Restrictions
- Remove app purchase checks from FTP/SFTP routes
- Make SFTP available to all users and suppliers

### 6. Testing
- Test SFTP connections (password & key auth)
- Validate imports/exports work via SFTP
- Verify supplier directory isolation

## Success Criteria
- [ ] SFTP works for all users (no app purchase needed)
- [ ] Bidirectional file transfer operational
- [ ] Existing FTP functionality preserved
- [ ] Product imports/exports work via SFTP

----

# Email Marketing issue/ delate all template
- **Reference:** https://app.clickup.com/t/86996mz8b
- **Status:** Urgent
- **Client's Feedback:** https://vimeo.com/1097349395/e56c3fb56d?share=copy

----

### Task Updates

Task: Email Marketing issue/ delate all template
Task Link: https://app.clickup.com/t/86996mz8b
Status: Waiting for feedback
Updates:
  - Drag and drop enabled for the first step
  - Fixed sender email configuration issue
  - Noreply as default sender email
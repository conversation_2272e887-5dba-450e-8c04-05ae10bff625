# Task Plan - June 26, 2025

## Project: DRM

### 1. Decathlon Switzerland Order Sync Verification
- **Reference:** [ClickUp #8697mgp3n](https://app.clickup.com/t/8697mgp3n)
- **Issue:** Orders from Decathlon Switzerland may not be syncing correctly
- **Required Changes:**
  - Check current sync logic for Decathlon Switzerland
  - Compare with Home24 channel ID order sync implementation
  - Determine if similar logic needs to be applied
- **Priority:** Urgent

### 2. Mirakl Connect Amazon Sync - Tax/Pricing Issue
- **Reference:** [ClickUp #8697mgp3n](https://app.clickup.com/t/8697mgp3n)
- **Issue:** Germany orders showing 16% tax instead of correct 19.5%
- **Required Changes:**
  - Fix tax calculation logic for Germany orders
  - Verify Mirakl Connect API response handling
  - Test with sample Germany order data
- **Priority:** Urgent

### 3. Amazon Italy Channel Name Display
- **Reference:** [ClickUp #8697mgp3n](https://app.clickup.com/t/8697mgp3n)
- **Issue:** Orders showing "Mirakl Connect" instead of actual marketplace name
- **Required Changes:**
  - Update logic to display actual marketplace name (e.g., "Amazon Italy")
  - Check API response structure for channel information
  - Update DRM order overview display
- **Priority:** Urgent

### 4. Email Marketing - Sender Configuration & Template Cleanup
- **Reference:** [ClickUp #86996mz8b](https://app.clickup.com/t/86996mz8b)
- **Issue 1:** Custom SMTP sender not available in campaigns despite being configured
- **Issue 2:** Outdated Dropamtix templates need removal
- **Required Changes:**
  - Investigate DKIM verification requirements for sender selection
  - Check custom SMTP configuration for expertisrocks account
  - Delete all existing Dropamtix templates completely
  - Ensure editor-only approach is implemented
- **Priority:** Urgent

## Notes
- Maintain communication with client for any urgent issues and tasks




## Task Update - June 26, 2025

### 1. Decathlon Switzerland Order Sync Verification
- **Reference:** [ClickUp #8697mgp3n](https://app.clickup.com/t/8697mgp3n)
- **Issue:** Orders from Decathlon Switzerland may not be syncing correctly
- **Applied Changes:**
  - Added new required field api_shop_id for all Mirakl channels in V1 shop settings
  - Added background logic to sync orders based on api_order_id

### 2. Mirakl Connect Amazon Sync - Tax/Pricing Issue
- **Reference:** [ClickUp #8697mgp3n](https://app.clickup.com/t/8697mgp3n)
- **Issue:** Germany orders showing 16% tax instead of correct 19.5%
- **Update:** No tax information found in API (Waiting for confirmation)

### 3. Amazon Italy Channel Name Display
- **Reference:** [ClickUp #8697mgp3n](https://app.clickup.com/t/8697mgp3n)
- **Issue:** Orders showing "Mirakl Connect" instead of actual marketplace name
- **Applied Changes:**
  - Added a new column channel_label in new_ordes table
  - Insert custom channel label with order data
  - Display alternative channel name if custom label exists

### 4. Email Marketing - Sender Configuration & Template Cleanup
- **Reference:** [ClickUp #86996mz8b](https://app.clickup.com/t/86996mz8b)
- **Update:** Will work on it after completing SwissPost API modifications


### 5. Item Color sync issue (DT > DRM) resolved
### 6. Colaborated with Sohel to debug and fix the price issue in Invoice
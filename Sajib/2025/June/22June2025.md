~ Project AlertCom ~
Tasks (Admin + Responder Web)

Add "Notification Only Event" option in launch forms (disable "Report to Locations")

1. Modify responder.html for notification-only mode:

 - Replace response buttons with: “Acknowledge and Participate”, etc.
 - Remove “Report to” and responder location section
 - Show “Date of Event” and “Time of Event”
 - Make Google Map a hyperlink
 - Display chat at top; ensure it's functional
 - Add "Summarize with AI" button for chat summary
 - Show list of participating responders
 - Add “My Actions” AI-generated checklist feature

2. Create new "Notification Only Event" dashboard

 - Update status labels as per rules
 - Show event launch date/time
 - Replace “Report to Locations” with chat section + AI summary
 - Replace “Response Profiles” with “Document and Role Management”

3. Implement Document and Role Management

 - Allow admin to upload files to events
 - Display files in user/responder views

4. Add AI "My Actions" feature for documents (role-based task extraction)

5. Add location update frequency selector (dropdown/slider) to launch config

6. Add "Notification Types" selection (Voice, SMS, App, Email)

*All Tasks will complete today for client feedback*


1. Notification-Only Event Mode**

- What to Implement

    - Add a new checkbox in launch forms called **"Notification Only Event"** after "Event Location".
    - If selected, disable the **"Report to Locations"** field.
    - Change responder UI when this mode is active.

    - Changes to Responder UI

        - Replace response buttons:

            - “Yes, I will respond” → “Acknowledge and Participate”
            - “No, but keep me updated” → “Acknowledge, but unable to participate”
            - “No, end updates” → “Remove me from event”
        - Remove:

            - “Report to” field
            - “Enroute”, “Delayed”, “Unable to Respond”, “Route Me” buttons
            - Location-sharing prompt
        - Add:

            - “Date of Event” and “Time of Event” using launch date/time
            - A **hyperlink**: “Map of Event Location”
            - Always-visible **chat area**
            - “Summarize with AI” button (calls OpenAI to summarize chat)
            - List of all participating responders


2. New Dashboard for Notification-Only Events**

    - Dashboard Layout & Terms Update

        - “Notified” – no change
        - “Acknowledged” → “Participating”
        - “Enroute” → “Unable to Participate”
        - “Delayed” → “Removed from Event”
        - “Unable/Cancelled” → “No Response” (calculated = Notified - sum of others)
        - Add **Event Launch Date and Time** at the top
        - Remove “Report to Locations”, **add chat with AI summary**
        - Replace **“Response Profiles”** section with **“Document and Role Management”**
        - Keep Google Map

3. Document and Role Management

- Features to Add

    - Admins can upload **PDF, Word, Images** to forms/templates.
    - Show uploaded docs in a **“Documents”** section for users during events.
    - Add a checklist UI (either in chat or document section).
    - Support both response and notification-only modes.

- AI Integration ("My Actions")

    - Use OpenAI API to read attached docs and generate **personalized task lists** based on:

        - User's role (e.g., nurse, firefighter)
        - Document contents
    - Show this under “My Actions” button in the responder view.



4. Location Update Frequency Control

- Fix Costly Location Updates

    - For response events only, add a **dropdown or slider** to launch form to select how often responder location updates.
    - Options: 5s, 10s, 15s, 30s, 60s, 300s, 600s, or custom.
    - Hide this option when “Notification Only Event” is selected.


5. Event-Based Notification Preferences

- New Notification Options on Launch

    - Add checkboxes for how responders are notified:

    * Voice Call
    * SMS
    * App
    * Email


6. Minor Text & UI Changes**

- Change “Enroute” button text to:
  “Enroute – Moving Toward Location”
- Only show this on **Response** mode.

